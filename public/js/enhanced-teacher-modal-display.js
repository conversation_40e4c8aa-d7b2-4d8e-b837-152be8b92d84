// Enhanced Teacher Modal Display Functions
// This file contains all the display and utility functions for the enhanced teacher modal

// Display enhanced teacher profile data in modal
function displayEnhancedTeacherProfile(teacher) {
    console.log('Displaying enhanced teacher profile:', teacher);

    // Basic information
    $('#modal-teacher-name').text(teacher.fullName || teacher.full_name || teacher.name || 'Unknown Teacher');
    $('#modal-teacher-designation').text(teacher.designation || 'Teacher');
    $('#modal-teacher-department').text(teacher.department || 'Academic Department');
    $('#modal-teacher-employee-id').text(teacher.employee_id || 'N/A');
    $('#modal-teacher-email').text(teacher.email || 'No email provided');
    $('#modal-teacher-phone').text(teacher.phone || 'No phone provided');
    $('#modal-teacher-joining-date').text(formatModalDate(teacher.joining_date) || 'Not specified');
    $('#modal-teacher-employment-type').text(capitalizeModalFirst(teacher.employment_type) || 'Not specified');

    // Additional personal details
    $('#modal-date-of-birth').text(formatModalDate(teacher.date_of_birth) || 'Not provided');
    $('#modal-gender').text(capitalizeModalFirst(teacher.gender) || 'Not specified');
    $('#modal-subjects-taught').text(teacher.subjects_taught || teacher.subjects || 'Not specified');
    $('#modal-classes-handled').text(teacher.classes_handled || 'Not specified');

    // Experience stats
    $('#modal-total-experience').text(teacher.total_experience_years || '0');
    $('#modal-teaching-experience').text(teacher.teaching_experience_years || '0');
    $('#modal-administrative-experience').text(teacher.administrative_experience_years || '0');

    // Profile image
    if (teacher.profile_image && teacher.profile_image !== 'null') {
        $('#modal-profile-image').attr('src', teacher.profile_image).removeClass('hidden');
        $('#modal-profile-image-placeholder').addClass('hidden');
    } else {
        const initials = getModalInitials(teacher);
        $('#modal-profile-image-placeholder').text(initials).removeClass('hidden');
    }

    // Display contact and administrative information
    displayModalContactInfo(teacher);
    displayModalAdministrativeInfo(teacher);

    // Display timelines
    displayModalEducationTimeline(teacher.educationTimeline || []);
    displayModalExperienceTimeline(teacher.experienceTimeline || []);

    // Display publications and research
    displayModalPublicationsResearch(teacher);
    displayModalCertifications(teacher);

    // Display skills and achievements
    displayModalSkillsAndLanguages(teacher.special_skills, teacher.languages_known);
    displayModalAchievements(teacher.awards_received, teacher.training_programs);

    // Display notes and previous organizations
    displayModalNotesAndOrganizations(teacher);
}

// Display education timeline in modal
function displayModalEducationTimeline(timeline) {
    const container = $('#modal-education-timeline');
    container.empty();

    if (timeline.length === 0) {
        container.html('<p class="text-gray-500 text-center">No educational information available</p>');
        return;
    }

    timeline.forEach((item, index) => {
        const timelineItem = $(`
            <div class="relative flex items-start">
                <div class="absolute left-4 w-3 h-3 bg-green-600 rounded-full border-2 border-white shadow-lg"></div>
                <div class="ml-12">
                    <div class="bg-gradient-to-r from-green-50 to-green-100 border-l-4 border-green-600 rounded-lg p-3">
                        <h5 class="font-bold text-md text-green-800">${item.title}</h5>
                        <p class="text-green-700 font-medium text-sm">${item.institution}</p>
                        ${item.board ? `<p class="text-xs text-green-600">Board: ${item.board}</p>` : ''}
                        ${item.stream ? `<p class="text-xs text-green-600">Stream: ${item.stream}</p>` : ''}
                        ${item.specialization ? `<p class="text-xs text-green-600">Specialization: ${item.specialization}</p>` : ''}
                        ${item.percentage ? `<p class="text-xs text-green-600">Percentage: ${item.percentage}%</p>` : ''}
                        ${item.thesis ? `<p class="text-xs text-green-600">Thesis: ${item.thesis}</p>` : ''}
                        <p class="text-xs text-gray-600 mt-2">
                            <i class="fas fa-calendar mr-1"></i>${item.year}
                        </p>
                    </div>
                </div>
            </div>
        `);

        container.append(timelineItem);
    });
}

// Display experience timeline in modal
function displayModalExperienceTimeline(timeline) {
    const container = $('#modal-experience-timeline');
    container.empty();

    if (timeline.length === 0) {
        container.html('<p class="text-gray-500 text-center">No experience information available</p>');
        return;
    }

    timeline.forEach((item, index) => {
        const timelineItem = $(`
            <div class="relative flex items-start">
                <div class="absolute left-4 w-3 h-3 ${item.isCurrent ? 'bg-purple-600' : 'bg-gray-400'} rounded-full border-2 border-white shadow-lg"></div>
                <div class="ml-12">
                    <div class="bg-gradient-to-r ${item.isCurrent ? 'from-purple-50 to-purple-100 border-purple-600' : 'from-gray-50 to-gray-100 border-gray-400'} border-l-4 rounded-lg p-3">
                        <h5 class="font-bold text-md ${item.isCurrent ? 'text-purple-800' : 'text-gray-800'}">${item.title}</h5>
                        <p class="${item.isCurrent ? 'text-purple-700' : 'text-gray-700'} font-medium text-sm">${item.institution}</p>
                        <p class="text-xs ${item.isCurrent ? 'text-purple-600' : 'text-gray-600'} mt-1">
                            <i class="fas fa-calendar mr-1"></i>${item.duration || item.year}
                        </p>
                        ${item.isCurrent ? '<span class="inline-block bg-purple-200 text-purple-800 text-xs px-2 py-1 rounded-full mt-2">Current Position</span>' : ''}
                    </div>
                </div>
            </div>
        `);

        container.append(timelineItem);
    });
}

// Display contact information in modal
function displayModalContactInfo(teacher) {
    $('#modal-alternate-phone').text(teacher.alternate_phone || 'Not provided');
    $('#modal-emergency-contact').text(teacher.emergency_contact || 'Not provided');
    $('#modal-address').text(teacher.address || 'Not provided');
    $('#modal-city').text(teacher.city || 'Not provided');
    $('#modal-state').text(teacher.state || 'Not provided');
    $('#modal-pincode').text(teacher.pincode || 'Not provided');
}

// Display administrative information in modal
function displayModalAdministrativeInfo(teacher) {
    $('#modal-office-location').text(teacher.office_location || 'Not specified');
    $('#modal-confirmation-date').text(formatModalDate(teacher.confirmation_date) || 'Not specified');
    $('#modal-last-promotion').text(formatModalDate(teacher.last_promotion_date) || 'Not specified');
    $('#modal-performance-rating').text(capitalizeModalFirst(teacher.performance_rating) || 'Not rated');
    $('#modal-current-salary').text(teacher.current_salary ? `₹${teacher.current_salary.toLocaleString()}` : 'Not specified');
}

// Display publications and research in modal
function displayModalPublicationsResearch(teacher) {
    // Publications
    const publicationsContainer = $('#modal-publications-list');
    if (teacher.publications) {
        const publicationsArray = teacher.publications.split(',');
        const publicationsHtml = publicationsArray.map(publication =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-file-alt text-teal-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${publication.trim()}</span>
            </div>`
        ).join('');
        publicationsContainer.html(publicationsHtml);
    } else {
        publicationsContainer.html('<p class="text-gray-500 text-xs">No publications available</p>');
    }

    // Research Papers
    const researchContainer = $('#modal-research-papers-list');
    if (teacher.research_papers) {
        const researchArray = teacher.research_papers.split(',');
        const researchHtml = researchArray.map(paper =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-microscope text-teal-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${paper.trim()}</span>
            </div>`
        ).join('');
        researchContainer.html(researchHtml);
    } else {
        researchContainer.html('<p class="text-gray-500 text-xs">No research papers available</p>');
    }

    // Conferences
    const conferencesContainer = $('#modal-conferences-list');
    if (teacher.conferences_attended) {
        const conferencesArray = teacher.conferences_attended.split(',');
        const conferencesHtml = conferencesArray.map(conference =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-users text-teal-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${conference.trim()}</span>
            </div>`
        ).join('');
        conferencesContainer.html(conferencesHtml);
    } else {
        conferencesContainer.html('<p class="text-gray-500 text-xs">No conferences attended</p>');
    }
}

// Display certifications in modal
function displayModalCertifications(teacher) {
    // Professional Certifications
    const certificationsContainer = $('#modal-certifications-list');
    if (teacher.professional_certifications) {
        const certificationsArray = teacher.professional_certifications.split(',');
        const certificationsHtml = certificationsArray.map(cert =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-award text-pink-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${cert.trim()}</span>
            </div>`
        ).join('');
        certificationsContainer.html(certificationsHtml);
    } else {
        certificationsContainer.html('<p class="text-gray-500 text-xs">No certifications available</p>');
    }

    // Other Qualifications
    const qualificationsContainer = $('#modal-other-qualifications-list');
    if (teacher.other_qualifications) {
        const qualificationsArray = teacher.other_qualifications.split(',');
        const qualificationsHtml = qualificationsArray.map(qual =>
            `<div class="flex items-start space-x-2 mb-2">
                <i class="fas fa-graduation-cap text-pink-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${qual.trim()}</span>
            </div>`
        ).join('');
        qualificationsContainer.html(qualificationsHtml);
    } else {
        qualificationsContainer.html('<p class="text-gray-500 text-xs">No additional qualifications available</p>');
    }
}
