<!-- Teacher Management Overview -->
<div class="mb-8">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-principal-light text-principal-primary">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Teachers</p>
                    <p class="text-2xl font-bold text-gray-900"><%= teachers.length %></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">High Performers</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) >= 80).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Need Attention</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) < 60).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-clock text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Overdue Tasks</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.reduce((sum, t) => sum + (t.overdue_lectures || 0), 0) %>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Teacher Performance Table -->
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-bold text-gray-900">Teacher Performance Dashboard</h2>
            <div class="flex items-center space-x-4">
                <!-- Export Button -->
                <button class="btn-principal-outline px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
                <!-- Refresh Button -->
                <button onclick="refreshTeacherData()" class="btn-principal px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Search and Filter -->
        <div class="mb-6 flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" id="search-teachers" placeholder="Search teachers by name or email..."
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
            </div>
            <div class="flex gap-2">
                <select id="filter-performance" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                    <option value="">All Performance</option>
                    <option value="excellent">Excellent (80%+)</option>
                    <option value="good">Good (60-79%)</option>
                    <option value="average">Average (40-59%)</option>
                    <option value="poor">Poor (<40%)</option>
                </select>
            </div>
        </div>

        <% if (teachers && teachers.length > 0) { %>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 table-principal">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Teacher
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Lectures
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Completion Rate
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Overdue
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Login
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Performance
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="teachers-table-body">
                        <% teachers.forEach(teacher => { %>
                            <%
                                const completionRate = teacher.completion_rate || 0;
                                const performanceLevel = completionRate >= 80 ? 'excellent' : completionRate >= 60 ? 'good' : completionRate >= 40 ? 'average' : 'poor';
                            %>
                            <tr class="hover:bg-gray-50 teacher-row"
                                data-name="<%= teacher.name.toLowerCase() %>"
                                data-email="<%= teacher.email.toLowerCase() %>"
                                data-performance="<%= performanceLevel %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-principal-light flex items-center justify-center">
                                                <span class="text-sm font-medium text-principal-primary">
                                                    <%= teacher.name.split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= teacher.name %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= teacher.email %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.delivered_lectures || 0 %></span>/<%= teacher.total_lectures || 0 %>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        Total lectures
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full <%= completionRate >= 80 ? 'bg-green-500' : completionRate >= 60 ? 'bg-blue-500' : completionRate >= 40 ? 'bg-yellow-500' : 'bg-red-500' %>"
                                                 style="width: <%= Math.min(completionRate, 100) %>%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">
                                            <%= parseFloat(completionRate || 0).toFixed(1) %>%
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <% if (teacher.overdue_lectures > 0) { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                            <%= teacher.overdue_lectures %>
                                        </span>
                                    <% } else { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>
                                            Up to date
                                        </span>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (teacher.last_login) { %>
                                        <%= new Date(teacher.last_login).toLocaleDateString() %>
                                    <% } else { %>
                                        Never
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= completionRate >= 80 ? 'bg-green-100 text-green-800' : completionRate >= 60 ? 'bg-blue-100 text-blue-800' : completionRate >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                        <% if (completionRate >= 80) { %>
                                            <i class="fas fa-star mr-1"></i> Excellent
                                        <% } else if (completionRate >= 60) { %>
                                            <i class="fas fa-thumbs-up mr-1"></i> Good
                                        <% } else if (completionRate >= 40) { %>
                                            <i class="fas fa-minus-circle mr-1"></i> Average
                                        <% } else { %>
                                            <i class="fas fa-times-circle mr-1"></i> Poor
                                        <% } %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button id="viewTeacherBtn-<%= teacher.id %>"
                                                class="view-teacher-btn text-principal-primary hover:text-principal-dark p-1 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="View Teacher Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="sendMessage(<%= teacher.id %>)"
                                                class="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                title="Send Message">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-user-friends text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900">No teachers found</h3>
                <p class="text-sm text-gray-500">Teacher data will appear here once teachers are added to the system.</p>
            </div>
        <% } %>
    </div>
</div>

<!-- Enhanced Teacher Details Modal -->
<div id="teacherModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[95vh] overflow-y-auto mx-4">
    <!-- Modal Header -->
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 id="teacherModalTitle" class="text-xl font-semibold flex items-center">
        <i class="fas fa-user-tie mr-3"></i>
        Enhanced Teacher Profile
      </h3>
      <button id="closeTeacherModalBtn" class="text-white hover:text-gray-200">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Modal Content -->
    <div id="teacherModalContent" class="p-6">
      <!-- Loading state -->
      <div id="modal-loading" class="flex items-center justify-center py-12">
        <i class="fas fa-spinner fa-spin text-3xl text-principal-primary mr-3"></i>
        <span class="text-lg text-gray-600">Loading teacher profile...</span>
      </div>

      <!-- Enhanced Profile Content (will be populated) -->
      <div id="enhanced-profile-content" class="hidden">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Personal Information Card -->
          <div class="lg:col-span-1">
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
                <h3 class="text-lg font-semibold">Personal Information</h3>
              </div>
              <div class="p-4">
                <!-- Profile Image -->
                <div class="flex flex-col items-center mb-4">
                  <div class="relative">
                    <div id="modal-profile-image-container" class="w-24 h-24 rounded-full bg-blue-600 border-4 border-blue-600 shadow-lg overflow-hidden flex items-center justify-center">
                      <div id="modal-profile-image-placeholder" class="text-2xl font-bold text-white">T</div>
                      <img id="modal-profile-image" class="w-full h-full object-cover hidden" src="" alt="Profile Image">
                    </div>
                  </div>
                  <h4 id="modal-teacher-name" class="text-lg font-bold text-gray-800 text-center">Loading...</h4>
                  <p id="modal-teacher-designation" class="text-blue-600 font-semibold text-center">Teacher</p>
                  <span id="modal-teacher-department" class="mt-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                    Academic Department
                  </span>
                </div>

                <!-- Basic Information -->
                <div class="space-y-3 text-sm">
                  <div class="flex items-center">
                    <i class="fas fa-id-badge text-blue-600 w-5"></i>
                    <span id="modal-teacher-employee-id" class="ml-3 text-gray-700">EMP0001</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-envelope text-blue-600 w-5"></i>
                    <span id="modal-teacher-email" class="ml-3 text-gray-700"><EMAIL></span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-phone text-blue-600 w-5"></i>
                    <span id="modal-teacher-phone" class="ml-3 text-gray-700">+91-XXXXXXXXXX</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-calendar text-blue-600 w-5"></i>
                    <span id="modal-teacher-joining-date" class="ml-3 text-gray-700">Joining Date</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-briefcase text-blue-600 w-5"></i>
                    <span id="modal-teacher-employment-type" class="ml-3 text-gray-700">Employment Type</span>
                  </div>
                </div>

                <!-- Additional Personal Details -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                  <h5 class="font-semibold text-gray-700 mb-3 text-sm">Additional Details</h5>
                  <div class="space-y-2 text-sm">
                    <div class="flex items-center">
                      <i class="fas fa-birthday-cake text-blue-600 w-5"></i>
                      <span id="modal-date-of-birth" class="ml-3 text-gray-700">Date of Birth</span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-venus-mars text-blue-600 w-5"></i>
                      <span id="modal-gender" class="ml-3 text-gray-700">Gender</span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-chalkboard-teacher text-blue-600 w-5"></i>
                      <span id="modal-subjects-taught" class="ml-3 text-gray-700">Subjects Taught</span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-users text-blue-600 w-5"></i>
                      <span id="modal-classes-handled" class="ml-3 text-gray-700">Classes Handled</span>
                    </div>
                  </div>
                </div>

                <!-- Quick Stats -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                  <h5 class="font-semibold text-gray-700 mb-3">Experience Summary</h5>
                  <div class="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div id="modal-total-experience" class="text-xl font-bold text-blue-600">0</div>
                      <div class="text-xs text-gray-600">Total Years</div>
                    </div>
                    <div>
                      <div id="modal-teaching-experience" class="text-xl font-bold text-blue-600">0</div>
                      <div class="text-xs text-gray-600">Teaching Years</div>
                    </div>
                  </div>
                  <div class="mt-3 text-center">
                    <div>
                      <div id="modal-administrative-experience" class="text-lg font-bold text-blue-600">0</div>
                      <div class="text-xs text-gray-600">Administrative Years</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Timeline Section -->
          <div class="lg:col-span-2">
            <div class="space-y-6">
              <!-- Educational Timeline -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-graduation-cap mr-3"></i>
                    Educational Timeline
                  </h3>
                </div>
                <div class="p-4">
                  <div class="relative">
                    <div class="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-300"></div>
                    <div id="modal-education-timeline" class="space-y-6">
                      <!-- Timeline entries will be populated here -->
                    </div>
                  </div>
                </div>
              </div>

              <!-- Professional Experience Timeline -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-briefcase mr-3"></i>
                    Professional Experience Timeline
                  </h3>
                </div>
                <div class="p-4">
                  <div class="relative">
                    <div class="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-300"></div>
                    <div id="modal-experience-timeline" class="space-y-6">
                      <!-- Timeline entries will be populated here -->
                    </div>
                  </div>
                </div>
              </div>

              <!-- Contact Information -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-address-book mr-3"></i>
                    Contact & Administrative Details
                  </h3>
                </div>
                <div class="p-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Contact Information -->
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-3 text-sm">Contact Information</h5>
                      <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                          <i class="fas fa-phone text-indigo-600 w-5"></i>
                          <span id="modal-alternate-phone" class="ml-3 text-gray-700">Alternate Phone</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-phone-alt text-indigo-600 w-5"></i>
                          <span id="modal-emergency-contact" class="ml-3 text-gray-700">Emergency Contact</span>
                        </div>
                        <div class="flex items-start">
                          <i class="fas fa-map-marker-alt text-indigo-600 w-5 mt-1"></i>
                          <div class="ml-3 text-gray-700">
                            <div id="modal-address" class="mb-1">Address</div>
                            <div class="text-xs text-gray-600">
                              <span id="modal-city">City</span>, <span id="modal-state">State</span> - <span id="modal-pincode">Pincode</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Administrative Details -->
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-3 text-sm">Administrative Details</h5>
                      <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                          <i class="fas fa-building text-indigo-600 w-5"></i>
                          <span id="modal-office-location" class="ml-3 text-gray-700">Office Location</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-calendar-check text-indigo-600 w-5"></i>
                          <span id="modal-confirmation-date" class="ml-3 text-gray-700">Confirmation Date</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-arrow-up text-indigo-600 w-5"></i>
                          <span id="modal-last-promotion" class="ml-3 text-gray-700">Last Promotion</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-star text-indigo-600 w-5"></i>
                          <span id="modal-performance-rating" class="ml-3 text-gray-700">Performance Rating</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-rupee-sign text-indigo-600 w-5"></i>
                          <span id="modal-current-salary" class="ml-3 text-gray-700">Current Salary</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Publications & Research -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-teal-600 to-teal-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-book mr-3"></i>
                    Publications & Research
                  </h3>
                </div>
                <div class="p-4">
                  <div class="space-y-4">
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Publications</h5>
                      <div id="modal-publications-list" class="text-sm text-gray-600">
                        <!-- Publications will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Research Papers</h5>
                      <div id="modal-research-papers-list" class="text-sm text-gray-600">
                        <!-- Research papers will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Conferences Attended</h5>
                      <div id="modal-conferences-list" class="text-sm text-gray-600">
                        <!-- Conferences will be populated here -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Professional Certifications -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-pink-600 to-pink-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-certificate mr-3"></i>
                    Professional Certifications & Qualifications
                  </h3>
                </div>
                <div class="p-4">
                  <div class="space-y-4">
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Professional Certifications</h5>
                      <div id="modal-certifications-list" class="text-sm text-gray-600">
                        <!-- Certifications will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Other Qualifications</h5>
                      <div id="modal-other-qualifications-list" class="text-sm text-gray-600">
                        <!-- Other qualifications will be populated here -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Skills and Achievements -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Skills -->
                <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                  <div class="bg-gradient-to-r from-orange-600 to-orange-700 text-white p-3">
                    <h4 class="text-md font-semibold">Skills & Languages</h4>
                  </div>
                  <div class="p-3">
                    <div class="mb-3">
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Special Skills</h5>
                      <div id="modal-skills-list" class="text-sm text-gray-600">
                        <!-- Skills will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Languages Known</h5>
                      <div id="modal-languages-list" class="text-sm text-gray-600">
                        <!-- Languages will be populated here -->
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Achievements -->
                <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                  <div class="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white p-3">
                    <h4 class="text-md font-semibold">Achievements</h4>
                  </div>
                  <div class="p-3">
                    <div class="mb-3">
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Awards Received</h5>
                      <div id="modal-awards-list" class="text-sm text-gray-600 space-y-1">
                        <!-- Awards will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Training Programs</h5>
                      <div id="modal-training-list" class="text-sm text-gray-600 space-y-1">
                        <!-- Training will be populated here -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Additional Notes -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-gray-600 to-gray-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-sticky-note mr-3"></i>
                    Additional Notes & Previous Organizations
                  </h3>
                </div>
                <div class="p-4">
                  <div class="space-y-4">
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Previous Organizations</h5>
                      <div id="modal-previous-organizations" class="text-sm text-gray-600">
                        <!-- Previous organizations will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Additional Notes</h5>
                      <div id="modal-notes" class="text-sm text-gray-600">
                        <!-- Notes will be populated here -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Footer -->
    <div id="teacherModalFooter" class="bg-gray-50 px-6 py-4 rounded-b-lg flex justify-end space-x-3">
      <button id="closeTeacherModalBtn2" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
        Close
      </button>
      <button id="printTeacherProfile" class="px-4 py-2 bg-principal-primary text-white rounded-md hover:bg-principal-secondary">
        <i class="fas fa-print mr-2"></i>Print Profile
      </button>
    </div>
  </div>
</div>

<script>
    // Set global flag IMMEDIATELY to prevent external JS from interfering
    window.enhancedTeacherModalLoaded = true;
    console.log('Enhanced teacher modal flag set:', window.enhancedTeacherModalLoaded);

    // Override external JS functions immediately
    window.openTeacherModal = function(teacherId) {
        console.log('External openTeacherModal called, redirecting to enhanced modal for teacher:', teacherId);
        console.log('Enhanced modal function available:', typeof window.openEnhancedTeacherModal);
        if (window.openEnhancedTeacherModal) {
            window.openEnhancedTeacherModal(teacherId);
        } else {
            console.error('Enhanced modal not yet available, waiting...');
            // Try again after a short delay
            setTimeout(function() {
                if (window.openEnhancedTeacherModal) {
                    console.log('Enhanced modal now available, calling it');
                    window.openEnhancedTeacherModal(teacherId);
                } else {
                    console.error('Enhanced modal still not available after delay');
                }
            }, 100);
        }
    };

    window.closeTeacherModal = function() {
        console.log('External closeTeacherModal called, redirecting to enhanced modal close');
        if (window.closeEnhancedTeacherModal) {
            window.closeEnhancedTeacherModal();
        }
    };

    // Send message functionality
    window.sendMessage = function(teacherId) {
        console.log('Send message functionality for teacher ID:', teacherId);
        alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
    };

    // Search and filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-teachers');
        const performanceFilter = document.getElementById('filter-performance');
        const rows = document.querySelectorAll('.teacher-row');

        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const performanceValue = performanceFilter.value;

            rows.forEach(row => {
                const name = row.dataset.name;
                const email = row.dataset.email;
                const performance = row.dataset.performance;

                const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
                const matchesPerformance = !performanceValue || performance === performanceValue;

                row.style.display = matchesSearch && matchesPerformance ? '' : 'none';
            });
        }

        if (searchInput) searchInput.addEventListener('input', filterTable);
        if (performanceFilter) performanceFilter.addEventListener('change', filterTable);
    });

    // Auto-refresh teacher performance data
    function refreshPageData() {
        fetch('/principal/api/teacher-performance')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the table without full page reload if needed
                }
            })
            .catch(error => {
                console.error('Error refreshing teacher performance:', error);
            });
    }

    // Start auto-refresh every 5 minutes
    startAutoRefresh(300000);

    // Enhanced Teacher Modal Functionality

    $(document).ready(function() {
        // Remove any existing event handlers from external JS
        $('.view-teacher-btn').off('click');
        $('[id^="viewTeacherBtn-"]').off('click');

        // View teacher details button (class-based)
        $(document).on('click', '.view-teacher-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const teacherId = $(this).data('teacher-id');
            openEnhancedTeacherModal(teacherId);
        });

        // View teacher details button (ID-based)
        $(document).on('click', '[id^="viewTeacherBtn-"]', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const teacherId = $(this).data('teacher-id');
            if (!teacherId) {
                // Extract from button ID if data attribute is missing
                const buttonId = $(this).attr('id');
                const extractedId = buttonId.replace('viewTeacherBtn-', '');
                if (extractedId) {
                    openEnhancedTeacherModal(extractedId);
                    return;
                }
            }
            openEnhancedTeacherModal(teacherId);
        });

        // Close modal buttons
        $(document).on('click', '#closeTeacherModalBtn, #closeTeacherModalBtn2', function() {
            closeEnhancedTeacherModal();
        });

        // Print teacher profile
        $(document).on('click', '#printTeacherProfile', function() {
            window.print();
        });

        // Close modal when clicking outside
        $(document).on('click', '#teacherModal', function(e) {
            if (e.target === this) {
                closeEnhancedTeacherModal();
            }
        });
    });

    // Open enhanced teacher modal (make it globally available)
    window.openEnhancedTeacherModal = function(teacherId) {
        console.log('Enhanced modal function called for teacher ID:', teacherId);
        console.log('Enhanced modal elements check starting...');

        const modal = document.getElementById('teacherModal');
        const loadingDiv = document.getElementById('modal-loading');
        const contentDiv = document.getElementById('enhanced-profile-content');

        if (!modal || !loadingDiv || !contentDiv) {
            console.error('Modal elements not found:', { modal: !!modal, loadingDiv: !!loadingDiv, contentDiv: !!contentDiv });
            alert('Error: Modal elements not found');
            return;
        }

        // Show modal and loading state
        modal.classList.remove('hidden');
        loadingDiv.classList.remove('hidden');
        contentDiv.classList.add('hidden');

        console.log('Fetching enhanced teacher data from API...');

        // Fetch enhanced teacher data
        fetch(`/principal/api/teacher/profile-enhanced?teacher_id=${teacherId}`)
            .then(response => {
                console.log('API response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                console.log('API response data:', result);
                if (result.success) {
                    displayEnhancedTeacherProfile(result.teacher);
                    loadingDiv.classList.add('hidden');
                    contentDiv.classList.remove('hidden');
                } else {
                    console.error('API returned error:', result.message);
                    showModalError(result.message || 'Error loading teacher profile');
                }
            })
            .catch(error => {
                console.error('Error fetching teacher profile:', error);
                showModalError('Failed to load teacher profile. Please try again.');
            });
    }

    // Close enhanced teacher modal (make it globally available)
    window.closeEnhancedTeacherModal = function() {
        const modal = document.getElementById('teacherModal');
        modal.classList.add('hidden');
    }

    // Display enhanced teacher profile data in modal
    function displayEnhancedTeacherProfile(teacher) {
        console.log('Displaying enhanced teacher profile:', teacher);

        // Basic information
        $('#modal-teacher-name').text(teacher.fullName || teacher.full_name || teacher.name || 'Unknown Teacher');
        $('#modal-teacher-designation').text(teacher.designation || 'Teacher');
        $('#modal-teacher-department').text(teacher.department || 'Academic Department');
        $('#modal-teacher-employee-id').text(teacher.employee_id || 'N/A');
        $('#modal-teacher-email').text(teacher.email || 'No email provided');
        $('#modal-teacher-phone').text(teacher.phone || 'No phone provided');
        $('#modal-teacher-joining-date').text(formatModalDate(teacher.joining_date) || 'Not specified');
        $('#modal-teacher-employment-type').text(capitalizeModalFirst(teacher.employment_type) || 'Not specified');

        // Additional personal details
        $('#modal-date-of-birth').text(formatModalDate(teacher.date_of_birth) || 'Not provided');
        $('#modal-gender').text(capitalizeModalFirst(teacher.gender) || 'Not specified');
        $('#modal-subjects-taught').text(teacher.subjects_taught || teacher.subjects || 'Not specified');
        $('#modal-classes-handled').text(teacher.classes_handled || 'Not specified');

        // Experience stats
        $('#modal-total-experience').text(teacher.total_experience_years || '0');
        $('#modal-teaching-experience').text(teacher.teaching_experience_years || '0');
        $('#modal-administrative-experience').text(teacher.administrative_experience_years || '0');

        // Profile image
        if (teacher.profile_image && teacher.profile_image !== 'null') {
            $('#modal-profile-image').attr('src', teacher.profile_image).removeClass('hidden');
            $('#modal-profile-image-placeholder').addClass('hidden');
        } else {
            const initials = getModalInitials(teacher);
            $('#modal-profile-image-placeholder').text(initials).removeClass('hidden');
        }

        // Display contact and administrative information
        displayModalContactInfo(teacher);
        displayModalAdministrativeInfo(teacher);

        // Display timelines
        displayModalEducationTimeline(teacher.educationTimeline || []);
        displayModalExperienceTimeline(teacher.experienceTimeline || []);

        // Display publications and research
        displayModalPublicationsResearch(teacher);
        displayModalCertifications(teacher);

        // Display skills and achievements
        displayModalSkillsAndLanguages(teacher.special_skills, teacher.languages_known);
        displayModalAchievements(teacher.awards_received, teacher.training_programs);

        // Display notes and previous organizations
        displayModalNotesAndOrganizations(teacher);
    }

    // Display education timeline in modal
    function displayModalEducationTimeline(timeline) {
        const container = $('#modal-education-timeline');
        container.empty();

        if (timeline.length === 0) {
            container.html('<p class="text-gray-500 text-center">No educational information available</p>');
            return;
        }

        timeline.forEach((item, index) => {
            const timelineItem = $(`
                <div class="relative flex items-start">
                    <div class="absolute left-4 w-3 h-3 bg-green-600 rounded-full border-2 border-white shadow-lg"></div>
                    <div class="ml-12">
                        <div class="bg-gradient-to-r from-green-50 to-green-100 border-l-4 border-green-600 rounded-lg p-3">
                            <h5 class="font-bold text-md text-green-800">${item.title}</h5>
                            <p class="text-green-700 font-medium text-sm">${item.institution}</p>
                            ${item.board ? `<p class="text-xs text-green-600">Board: ${item.board}</p>` : ''}
                            ${item.stream ? `<p class="text-xs text-green-600">Stream: ${item.stream}</p>` : ''}
                            ${item.specialization ? `<p class="text-xs text-green-600">Specialization: ${item.specialization}</p>` : ''}
                            ${item.percentage ? `<p class="text-xs text-green-600">Percentage: ${item.percentage}%</p>` : ''}
                            ${item.thesis ? `<p class="text-xs text-green-600">Thesis: ${item.thesis}</p>` : ''}
                            <p class="text-xs text-gray-600 mt-2">
                                <i class="fas fa-calendar mr-1"></i>${item.year}
                            </p>
                        </div>
                    </div>
                </div>
            `);

            container.append(timelineItem);
        });
    }

    // Display experience timeline in modal
    function displayModalExperienceTimeline(timeline) {
        const container = $('#modal-experience-timeline');
        container.empty();

        if (timeline.length === 0) {
            container.html('<p class="text-gray-500 text-center">No experience information available</p>');
            return;
        }

        timeline.forEach((item, index) => {
            const timelineItem = $(`
                <div class="relative flex items-start">
                    <div class="absolute left-4 w-3 h-3 ${item.isCurrent ? 'bg-purple-600' : 'bg-gray-400'} rounded-full border-2 border-white shadow-lg"></div>
                    <div class="ml-12">
                        <div class="bg-gradient-to-r ${item.isCurrent ? 'from-purple-50 to-purple-100 border-purple-600' : 'from-gray-50 to-gray-100 border-gray-400'} border-l-4 rounded-lg p-3">
                            <h5 class="font-bold text-md ${item.isCurrent ? 'text-purple-800' : 'text-gray-800'}">${item.title}</h5>
                            <p class="${item.isCurrent ? 'text-purple-700' : 'text-gray-700'} font-medium text-sm">${item.institution}</p>
                            <p class="text-xs ${item.isCurrent ? 'text-purple-600' : 'text-gray-600'} mt-1">
                                <i class="fas fa-calendar mr-1"></i>${item.duration || item.year}
                            </p>
                            ${item.isCurrent ? '<span class="inline-block bg-purple-200 text-purple-800 text-xs px-2 py-1 rounded-full mt-2">Current Position</span>' : ''}
                        </div>
                    </div>
                </div>
            `);

            container.append(timelineItem);
        });
    }

    // Display contact information in modal
    function displayModalContactInfo(teacher) {
        $('#modal-alternate-phone').text(teacher.alternate_phone || 'Not provided');
        $('#modal-emergency-contact').text(teacher.emergency_contact || 'Not provided');
        $('#modal-address').text(teacher.address || 'Not provided');
        $('#modal-city').text(teacher.city || 'Not provided');
        $('#modal-state').text(teacher.state || 'Not provided');
        $('#modal-pincode').text(teacher.pincode || 'Not provided');
    }

    // Display administrative information in modal
    function displayModalAdministrativeInfo(teacher) {
        $('#modal-office-location').text(teacher.office_location || 'Not specified');
        $('#modal-confirmation-date').text(formatModalDate(teacher.confirmation_date) || 'Not specified');
        $('#modal-last-promotion').text(formatModalDate(teacher.last_promotion_date) || 'Not specified');
        $('#modal-performance-rating').text(capitalizeModalFirst(teacher.performance_rating) || 'Not rated');
        $('#modal-current-salary').text(teacher.current_salary ? `₹${teacher.current_salary.toLocaleString()}` : 'Not specified');
    }

    // Display publications and research in modal
    function displayModalPublicationsResearch(teacher) {
        // Publications
        const publicationsContainer = $('#modal-publications-list');
        if (teacher.publications) {
            const publicationsArray = teacher.publications.split(',');
            const publicationsHtml = publicationsArray.map(publication =>
                `<div class="flex items-start space-x-2 mb-2">
                    <i class="fas fa-file-alt text-teal-600 mt-1 text-xs"></i>
                    <span class="text-gray-700 text-xs">${publication.trim()}</span>
                </div>`
            ).join('');
            publicationsContainer.html(publicationsHtml);
        } else {
            publicationsContainer.html('<p class="text-gray-500 text-xs">No publications available</p>');
        }

        // Research Papers
        const researchContainer = $('#modal-research-papers-list');
        if (teacher.research_papers) {
            const researchArray = teacher.research_papers.split(',');
            const researchHtml = researchArray.map(paper =>
                `<div class="flex items-start space-x-2 mb-2">
                    <i class="fas fa-microscope text-teal-600 mt-1 text-xs"></i>
                    <span class="text-gray-700 text-xs">${paper.trim()}</span>
                </div>`
            ).join('');
            researchContainer.html(researchHtml);
        } else {
            researchContainer.html('<p class="text-gray-500 text-xs">No research papers available</p>');
        }

        // Conferences
        const conferencesContainer = $('#modal-conferences-list');
        if (teacher.conferences_attended) {
            const conferencesArray = teacher.conferences_attended.split(',');
            const conferencesHtml = conferencesArray.map(conference =>
                `<div class="flex items-start space-x-2 mb-2">
                    <i class="fas fa-users text-teal-600 mt-1 text-xs"></i>
                    <span class="text-gray-700 text-xs">${conference.trim()}</span>
                </div>`
            ).join('');
            conferencesContainer.html(conferencesHtml);
        } else {
            conferencesContainer.html('<p class="text-gray-500 text-xs">No conferences attended</p>');
        }
    }

    // Display certifications in modal
    function displayModalCertifications(teacher) {
        // Professional Certifications
        const certificationsContainer = $('#modal-certifications-list');
        if (teacher.professional_certifications) {
            const certificationsArray = teacher.professional_certifications.split(',');
            const certificationsHtml = certificationsArray.map(cert =>
                `<div class="flex items-start space-x-2 mb-2">
                    <i class="fas fa-award text-pink-600 mt-1 text-xs"></i>
                    <span class="text-gray-700 text-xs">${cert.trim()}</span>
                </div>`
            ).join('');
            certificationsContainer.html(certificationsHtml);
        } else {
            certificationsContainer.html('<p class="text-gray-500 text-xs">No certifications available</p>');
        }

        // Other Qualifications
        const qualificationsContainer = $('#modal-other-qualifications-list');
        if (teacher.other_qualifications) {
            const qualificationsArray = teacher.other_qualifications.split(',');
            const qualificationsHtml = qualificationsArray.map(qual =>
                `<div class="flex items-start space-x-2 mb-2">
                    <i class="fas fa-graduation-cap text-pink-600 mt-1 text-xs"></i>
                    <span class="text-gray-700 text-xs">${qual.trim()}</span>
                </div>`
            ).join('');
            qualificationsContainer.html(qualificationsHtml);
        } else {
            qualificationsContainer.html('<p class="text-gray-500 text-xs">No additional qualifications available</p>');
        }
    }

    // Display notes and previous organizations in modal
    function displayModalNotesAndOrganizations(teacher) {
        // Previous Organizations
        const organizationsContainer = $('#modal-previous-organizations');
        if (teacher.previous_organizations) {
            const organizationsArray = teacher.previous_organizations.split(',');
            const organizationsHtml = organizationsArray.map(org =>
                `<div class="flex items-start space-x-2 mb-2">
                    <i class="fas fa-building text-gray-600 mt-1 text-xs"></i>
                    <span class="text-gray-700 text-xs">${org.trim()}</span>
                </div>`
            ).join('');
            organizationsContainer.html(organizationsHtml);
        } else {
            organizationsContainer.html('<p class="text-gray-500 text-xs">No previous organizations listed</p>');
        }

        // Notes
        const notesContainer = $('#modal-notes');
        if (teacher.notes) {
            notesContainer.html(`<p class="text-gray-700 text-xs leading-relaxed">${teacher.notes}</p>`);
        } else {
            notesContainer.html('<p class="text-gray-500 text-xs">No additional notes available</p>');
        }
    }

    // Display skills and languages in modal
    function displayModalSkillsAndLanguages(skills, languages) {
        // Skills
        const skillsContainer = $('#modal-skills-list');
        if (skills) {
            const skillsArray = skills.split(',');
            const skillsHtml = skillsArray.map(skill =>
                `<span class="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">${skill.trim()}</span>`
            ).join('');
            skillsContainer.html(skillsHtml);
        } else {
            skillsContainer.html('<p class="text-gray-500 text-xs">No skills specified</p>');
        }

        // Languages
        const languagesContainer = $('#modal-languages-list');
        if (languages) {
            const languagesArray = languages.split(',');
            const languagesHtml = languagesArray.map(language =>
                `<span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">${language.trim()}</span>`
            ).join('');
            languagesContainer.html(languagesHtml);
        } else {
            languagesContainer.html('<p class="text-gray-500 text-xs">No languages specified</p>');
        }
    }

    // Display achievements in modal
    function displayModalAchievements(awards, training) {
        // Awards
        const awardsContainer = $('#modal-awards-list');
        if (awards) {
            const awardsArray = awards.split(',');
            const awardsHtml = awardsArray.map(award =>
                `<div class="flex items-start space-x-2">
                    <i class="fas fa-trophy text-yellow-600 mt-1 text-xs"></i>
                    <span class="text-gray-700 text-xs">${award.trim()}</span>
                </div>`
            ).join('');
            awardsContainer.html(awardsHtml);
        } else {
            awardsContainer.html('<p class="text-gray-500 text-xs">No awards specified</p>');
        }

        // Training
        const trainingContainer = $('#modal-training-list');
        if (training) {
            const trainingArray = training.split(',');
            const trainingHtml = trainingArray.map(program =>
                `<div class="flex items-start space-x-2">
                    <i class="fas fa-certificate text-green-600 mt-1 text-xs"></i>
                    <span class="text-gray-700 text-xs">${program.trim()}</span>
                </div>`
            ).join('');
            trainingContainer.html(trainingHtml);
        } else {
            trainingContainer.html('<p class="text-gray-500 text-xs">No training programs specified</p>');
        }
    }

    // Modal utility functions
    function getModalInitials(teacher) {
        const name = teacher.fullName || teacher.name || teacher.username || 'T';
        return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
    }

    function formatModalDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    function capitalizeModalFirst(str) {
        if (!str) return '';
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    function showModalError(message) {
        const loadingDiv = document.getElementById('modal-loading');
        loadingDiv.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <p class="text-gray-600">${message}</p>
                <button onclick="closeEnhancedTeacherModal()" class="mt-4 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                    Close
                </button>
            </div>
        `;
    }
</script>
