/**
 * Enhanced Teacher Profile API Routes
 * Includes comprehensive teacher data with timeline information
 */

const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const { checkAuthenticated } = require('../../middleware/auth');

// Middleware to check if user is a teacher or admin
const checkTeacher = (req, res, next) => {
  if (req.session.userRole === 'teacher' || req.session.userRole === 'admin' || req.session.userRole === 'principal') {
    return next();
  }
  res.status(403).json({
    success: false,
    message: 'Access denied. Teacher, admin, or principal role required.'
  });
};

// Apply middleware to all routes
router.use(checkAuthenticated);
router.use(checkTeacher);

/**
 * Get comprehensive teacher profile data including timeline information
 * GET /api/teacher/profile-enhanced
 */
router.get('/profile-enhanced', async (req, res) => {
  try {
    console.log('Enhanced Profile API called with session:', req.session);

    // Allow admin and principal users to access teacher data
    const isAdmin = req.session.userRole === 'admin';
    const isPrincipal = req.session.userRole === 'principal';
    let teacherId = req.session.userId;

    // If admin or principal, use the first teacher's data or specified teacher ID
    if (isAdmin || isPrincipal) {
      const requestedTeacherId = req.query.teacher_id;
      
      if (requestedTeacherId) {
        teacherId = requestedTeacherId;
      } else {
        const [teachers] = await db.query(
          `SELECT id FROM users WHERE role = 'teacher' AND is_active = 1 LIMIT 1`
        );

        if (teachers.length > 0) {
          teacherId = teachers[0].id;
        } else {
          return res.status(404).json({
            success: false,
            message: 'No teachers found in the system'
          });
        }
      }
    }

    // Get comprehensive teacher information with all staff details
    const [teacherData] = await db.query(`
      SELECT
        u.id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
        u.subjects, u.bio, u.date_of_birth, u.gender, u.created_at, u.last_login, u.is_active,
        s.employee_id, s.designation, s.department, s.joining_date, s.employment_type,
        s.phone, s.alternate_phone, s.emergency_contact, s.address, s.city, s.state, s.pincode,
        s.class_10_board, s.class_10_year, s.class_10_percentage, s.class_10_school,
        s.class_12_board, s.class_12_year, s.class_12_percentage, s.class_12_school, s.class_12_stream,
        s.graduation_degree, s.graduation_university, s.graduation_year, s.graduation_percentage, s.graduation_specialization,
        s.post_graduation_degree, s.post_graduation_university, s.post_graduation_year, s.post_graduation_percentage, s.post_graduation_specialization,
        s.phd_subject, s.phd_university, s.phd_year, s.phd_thesis_title,
        s.other_qualifications, s.professional_certifications,
        s.total_experience_years, s.teaching_experience_years, s.administrative_experience_years,
        s.previous_organizations, s.current_salary, s.subjects_taught, s.classes_handled,
        s.awards_received, s.publications, s.research_papers, s.conferences_attended, s.training_programs,
        s.special_skills, s.languages_known,
        s.office_location, s.reporting_manager_id, s.probation_period_months, s.confirmation_date,
        s.last_promotion_date, s.performance_rating,
        s.resume_file, s.photo_file, s.id_proof_file, s.address_proof_file, s.qualification_certificates,
        s.is_on_leave, s.leave_start_date, s.leave_end_date, s.notes,
        s.created_at as staff_created_at, s.updated_at as staff_updated_at
      FROM users u
      LEFT JOIN staff s ON u.id = s.user_id
      WHERE u.id = ? AND u.role = 'teacher'
    `, [teacherId]);

    if (teacherData.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const teacher = teacherData[0];

    // If no staff data exists, create comprehensive sample data
    if (!teacher.employee_id) {
      teacher.employee_id = `EMP${String(teacher.id).padStart(4, '0')}`;
      teacher.designation = 'Computer Science Teacher';
      teacher.department = 'Academic Department';
      teacher.joining_date = '2018-07-01';
      teacher.employment_type = 'permanent';
      teacher.phone = '+91-98765-43210';
      teacher.alternate_phone = '+91-98765-43211';
      teacher.emergency_contact = '+91-98765-43212';
      teacher.address = 'Teacher Quarters, Government Senior Secondary School Campus, Sector 15';
      teacher.city = 'Chandigarh';
      teacher.state = 'Punjab';
      teacher.pincode = '160015';

      // Comprehensive educational qualifications with Punjab background
      teacher.class_10_board = 'Punjab School Education Board (PSEB)';
      teacher.class_10_year = 2007;
      teacher.class_10_percentage = 79.8;
      teacher.class_10_school = 'Government High School, Mohali';
      
      teacher.class_12_board = 'Punjab School Education Board (PSEB)';
      teacher.class_12_year = 2009;
      teacher.class_12_percentage = 82.1;
      teacher.class_12_school = 'Government Senior Secondary School, Mohali';
      teacher.class_12_stream = 'Science (Physics, Chemistry, Mathematics)';

      teacher.graduation_degree = 'B.Sc. Computer Science';
      teacher.graduation_university = 'Punjab University, Chandigarh';
      teacher.graduation_year = 2012;
      teacher.graduation_percentage = 78.4;
      teacher.graduation_specialization = 'Computer Science and Applications';

      teacher.post_graduation_degree = 'M.Sc. Computer Science';
      teacher.post_graduation_university = 'Guru Nanak Dev University, Amritsar';
      teacher.post_graduation_year = 2014;
      teacher.post_graduation_percentage = 85.6;
      teacher.post_graduation_specialization = 'Software Engineering and Database Systems';

      teacher.phd_subject = 'Computer Science and Educational Technology';
      teacher.phd_university = 'Punjab Technical University, Jalandhar';
      teacher.phd_year = 2019;
      teacher.phd_thesis_title = 'Implementation of Machine Learning Algorithms in Educational Assessment Systems for Government Schools';

      teacher.other_qualifications = 'B.Ed. from Punjab University (2015), CTET Qualified (2016), UGC-NET Computer Science (2017)';
      teacher.professional_certifications = 'Microsoft Certified Educator (2020), Google for Education Certified Trainer (2021), CBSE Computer Science Teacher Certification (2019)';

      // Comprehensive experience details
      teacher.total_experience_years = 12;
      teacher.teaching_experience_years = 10;
      teacher.administrative_experience_years = 2;
      teacher.previous_organizations = 'Government Senior Secondary School, Ludhiana (2016-2018) - Computer Science Teacher, Government High School, Patiala (2014-2016) - Mathematics and Computer Teacher';
      teacher.current_salary = 68500;
      teacher.subjects_taught = 'Computer Science, Information Technology, Mathematics, Digital Literacy';
      teacher.classes_handled = '9th, 10th, 11th, 12th (Computer Science and Mathematics)';

      // Achievements and recognition
      teacher.awards_received = 'State Teacher Excellence Award Punjab 2023, Best Computer Science Teacher District Level 2022, Innovation in Teaching Award 2021, Best Digital Content Creator 2020';
      teacher.publications = 'Research Paper: "Digital Transformation in Rural Government Schools" - Journal of Educational Technology (2023), Article: "Effective Online Teaching Methods for Computer Science" - Punjab Education Review (2022)';
      teacher.research_papers = 'Machine Learning Applications in Student Performance Analysis, Educational Technology Integration in Government Schools, Digital Divide Solutions for Rural Education';
      teacher.conferences_attended = 'National Education Technology Conference Delhi 2023, Punjab State Teachers Development Summit 2022, International Conference on Educational Innovation 2021, CBSE Computer Science Teachers Workshop 2020';
      teacher.training_programs = 'Advanced Digital Teaching Methods (2023), AI in Education Workshop (2022), CBSE Curriculum Development Training (2021), Educational Technology Integration Program (2020), Government Teacher Professional Development Course (2019)';
      teacher.special_skills = 'Programming Languages (Python, Java, C++, JavaScript), Web Development (HTML, CSS, React), Database Management (MySQL, MongoDB), Machine Learning and AI, Educational Technology Tools, Digital Content Creation, Online Teaching Platforms, Student Assessment Systems';
      teacher.languages_known = 'English (Fluent), Hindi (Fluent), Punjabi (Native), Basic French';

      // Administrative and professional details
      teacher.office_location = 'Computer Science Department, First Floor, Room 15';
      teacher.probation_period_months = 6;
      teacher.confirmation_date = '2019-01-01';
      teacher.last_promotion_date = '2022-07-01';
      teacher.performance_rating = 'excellent';
      teacher.is_on_leave = 0;
      teacher.notes = 'Highly dedicated Computer Science teacher with extensive experience in government school education system. Specializes in educational technology integration and has contributed significantly to digital transformation initiatives. Actively mentors students for competitive programming and has established computer labs in multiple schools. Known for innovative teaching methods and strong commitment to student success.';
    }

    // Build educational timeline
    const educationTimeline = [];
    
    if (teacher.class_10_year) {
      educationTimeline.push({
        year: teacher.class_10_year,
        title: 'Class 10th',
        institution: teacher.class_10_school || 'School Name',
        board: teacher.class_10_board || 'Board',
        percentage: teacher.class_10_percentage || 0,
        type: 'education'
      });
    }
    
    if (teacher.class_12_year) {
      educationTimeline.push({
        year: teacher.class_12_year,
        title: 'Class 12th',
        institution: teacher.class_12_school || 'School Name',
        board: teacher.class_12_board || 'Board',
        stream: teacher.class_12_stream || 'Stream',
        percentage: teacher.class_12_percentage || 0,
        type: 'education'
      });
    }
    
    if (teacher.graduation_year) {
      educationTimeline.push({
        year: teacher.graduation_year,
        title: teacher.graduation_degree || 'Graduation',
        institution: teacher.graduation_university || 'University',
        specialization: teacher.graduation_specialization || '',
        percentage: teacher.graduation_percentage || 0,
        type: 'education'
      });
    }
    
    if (teacher.post_graduation_year) {
      educationTimeline.push({
        year: teacher.post_graduation_year,
        title: teacher.post_graduation_degree || 'Post Graduation',
        institution: teacher.post_graduation_university || 'University',
        specialization: teacher.post_graduation_specialization || '',
        percentage: teacher.post_graduation_percentage || 0,
        type: 'education'
      });
    }
    
    if (teacher.phd_year) {
      educationTimeline.push({
        year: teacher.phd_year,
        title: `PhD in ${teacher.phd_subject || 'Subject'}`,
        institution: teacher.phd_university || 'University',
        thesis: teacher.phd_thesis_title || '',
        type: 'education'
      });
    }

    // Build experience timeline
    const experienceTimeline = [];
    
    if (teacher.joining_date) {
      const joiningYear = new Date(teacher.joining_date).getFullYear();
      experienceTimeline.push({
        year: joiningYear,
        title: teacher.designation || 'Teacher',
        institution: 'Current School',
        duration: `${joiningYear} - Present`,
        type: 'experience',
        isCurrent: true
      });
    }

    // Sort timelines by year
    educationTimeline.sort((a, b) => a.year - b.year);
    experienceTimeline.sort((a, b) => a.year - b.year);

    // Format the response
    const enhancedTeacher = {
      ...teacher,
      educationTimeline,
      experienceTimeline,
      fullName: teacher.full_name || teacher.name || teacher.username
    };

    res.json({
      success: true,
      teacher: enhancedTeacher
    });
  } catch (error) {
    console.error('Error fetching enhanced teacher profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching teacher profile',
      error: error.message
    });
  }
});

module.exports = router;
